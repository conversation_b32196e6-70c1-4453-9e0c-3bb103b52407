<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格式刷功能测试</title>
    <script src="https://www.72crm.com/npm/tinymce/tinymce.min.js"></script>
</head>
<body>
    <h1>TinyMCE 格式刷功能测试</h1>
    <div id="editor-container">
        <textarea id="mytextarea">
            <p>这是一段普通文本。</p>
            <p style="color: red; font-size: 18px; font-weight: bold;">这是一段红色粗体大字文本。</p>
            <p>这是另一段普通文本，可以用来测试格式刷功能。</p>
        </textarea>
    </div>

    <script>
        tinymce.init({
            selector: '#mytextarea',
            height: 400,
            menubar: false,
            statusbar: false,
            plugins: 'lists link image table paste code help wordcount',
            toolbar: 'undo redo | formatpainter | bold italic underline strikethrough | fontselect fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist | table | link | removeformat',
            paste_data_images: true,
            paste_enable_default_filters: true,
            placeholder: '输入文字内容',
            content_style: ' * {color: #262626; margin: 0;} body { margin: 8px; font-size: 14px; }',
            paste_retain_style_properties: 'all',
            toolbar_mode: 'sliding',
            setup: function(editor) {
                // 格式刷相关变量
                let formatPainterActive = false;
                let copiedFormat = null;
                
                // 添加自定义格式刷按钮
                editor.ui.registry.addButton('formatpainter', {
                    text: '格式刷',
                    icon: 'format-painter',
                    tooltip: '格式刷 - 先选择要复制格式的文本，点击格式刷，再点击要应用格式的文本',
                    onAction: function() {
                        if (!formatPainterActive) {
                            // 激活格式刷模式
                            const selection = editor.selection;
                            const selectedNode = selection.getNode();
                            
                            if (selectedNode) {
                                // 获取选中文本的格式
                                copiedFormat = {
                                    fontFamily: editor.dom.getStyle(selectedNode, 'font-family'),
                                    fontSize: editor.dom.getStyle(selectedNode, 'font-size'),
                                    fontWeight: editor.dom.getStyle(selectedNode, 'font-weight'),
                                    fontStyle: editor.dom.getStyle(selectedNode, 'font-style'),
                                    textDecoration: editor.dom.getStyle(selectedNode, 'text-decoration'),
                                    color: editor.dom.getStyle(selectedNode, 'color'),
                                    backgroundColor: editor.dom.getStyle(selectedNode, 'background-color'),
                                    textAlign: editor.dom.getStyle(selectedNode, 'text-align')
                                };
                                
                                formatPainterActive = true;
                                editor.getBody().style.cursor = 'crosshair';
                                
                                // 显示提示
                                editor.notificationManager.open({
                                    text: '格式刷已激活，点击要应用格式的文本',
                                    type: 'info',
                                    timeout: 3000
                                });
                                
                                console.log('格式刷已激活，复制的格式:', copiedFormat);
                            } else {
                                editor.notificationManager.open({
                                    text: '请先选择要复制格式的文本',
                                    type: 'warning',
                                    timeout: 2000
                                });
                            }
                        } else {
                            // 取消格式刷模式
                            formatPainterActive = false;
                            copiedFormat = null;
                            editor.getBody().style.cursor = 'auto';
                            
                            editor.notificationManager.open({
                                text: '格式刷已取消',
                                type: 'info',
                                timeout: 2000
                            });
                        }
                    }
                });
                
                // 监听点击事件应用格式
                editor.on('click', function(e) {
                    if (formatPainterActive && copiedFormat) {
                        const selection = editor.selection;
                        const selectedNode = selection.getNode();
                        
                        if (selectedNode) {
                            // 应用复制的格式
                            Object.keys(copiedFormat).forEach(style => {
                                if (copiedFormat[style]) {
                                    editor.dom.setStyle(selectedNode, style, copiedFormat[style]);
                                }
                            });
                            
                            // 取消格式刷模式
                            formatPainterActive = false;
                            copiedFormat = null;
                            editor.getBody().style.cursor = 'auto';
                            
                            editor.notificationManager.open({
                                text: '格式已应用',
                                type: 'success',
                                timeout: 2000
                            });
                            
                            console.log('格式已应用到:', selectedNode);
                        }
                    }
                });
                
                editor.on('init', function() {
                    console.log('TinyMCE 编辑器已初始化');
                });
            }
        });
    </script>

    <div style="margin-top: 20px; padding: 20px; background-color: #f5f5f5; border-radius: 5px;">
        <h3>使用说明：</h3>
        <ol>
            <li>选择要复制格式的文本（比如红色粗体大字文本）</li>
            <li>点击工具栏中的"格式刷"按钮</li>
            <li>鼠标会变成十字形，表示格式刷已激活</li>
            <li>点击要应用格式的文本（比如普通文本）</li>
            <li>格式会被应用，格式刷自动取消</li>
        </ol>
        <p><strong>注意：</strong>如果格式刷按钮不显示，说明TinyMCE版本可能不支持自定义按钮，或者需要其他配置。</p>
    </div>
</body>
</html>
